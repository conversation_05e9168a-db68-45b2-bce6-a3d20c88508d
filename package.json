{"name": "@e7w/easy-model", "version": "0.1.5", "module": "./dist/index.es.js", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "type": "module", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.es.js", "require": "./dist/index.umd.js"}}, "scripts": {"dev": "vite --host", "build": "npm run build:lib && npm run build:dts", "build:lib": "rimraf dist && tsc && vite build", "build:dts": "dts-bundle-generator --config ./dts-bundle-generator.config.cjs", "test": "vitest", "test:coverage": "vitest --coverage", "lint:scripts": "eslint . --fix --ext .ts", "lint:styles": "stylelint ./**/*.{css,scss}", "format:scripts": "prettier . --write", "format:styles": "stylelint ./**/*.{css,scss} --fix", "format": "npm run format:scripts && npm run format:styles", "prepare": "husky"}, "peerDependencies": {"react": ">=17.0.0", "zod": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.24.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.13.5", "@types/react": ">=17.0.0", "@vitejs/plugin-react-swc": "^3.8.1", "@vitest/coverage-v8": "^3.0.7", "copyfiles": "^2.4.1", "dts-bundle-generator": "^9.5.1", "eslint": "^9.24.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-autofix": "^2.2.0", "eslint-plugin-prettier": "^5.2.3", "globals": "^16.0.0", "husky": "^9.1.7", "jiti": "^2.4.2", "jsdom": "^26.0.0", "lint-staged": "^15.4.3", "postcss": "^8.5.3", "postcss-scss": "^4.0.9", "prettier": "^3.5.2", "rimraf": "^6.0.1", "stylelint": "^16.14.1", "stylelint-config-recommended": "^15.0.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^5.0.3", "ts-node": "^10.9.2", "typescript": "^5.7.3", "typescript-eslint": "^8.29.1", "vite": "^6.2.0", "vitest": "^3.0.7"}, "files": ["dist", "README.md", "LICENSE.md"], "license": "MIT"}